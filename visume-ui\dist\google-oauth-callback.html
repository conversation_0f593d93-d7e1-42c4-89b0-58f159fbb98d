<!DOCTYPE html>
<html>
  <head>
    <title>Google OAuth Callback</title>
    <meta charset="UTF-8" />
  </head>
  <body>
    <script>
      (function () {
        function parseHash(hash) {
          const params = new URLSearchParams(hash.replace(/^#/, ""));
          return {
            accessToken: params.get("access_token"),
            idToken: params.get("id_token")
          };
        }
        const { accessToken, idToken } = parseHash(window.location.hash);
        if (window.opener && (accessToken || idToken)) {
          window.opener.postMessage(
            { type: "google_oauth_token", accessToken, idToken },
            window.location.origin
          );
        }
        window.close();
      })();
    </script>
    <p>You may close this window.</p>
  </body>
</html>