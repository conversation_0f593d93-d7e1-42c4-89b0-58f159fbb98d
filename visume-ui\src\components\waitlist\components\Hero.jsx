"use client"

import { motion, AnimatePresence } from "framer-motion"

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  CheckCircle,
  Mail,
  Loader2,
  Monitor,
  Palette,
  Zap,
  Search,
  Settings,
  BarChart3,
  Eye,
  Building,
} from "lucide-react"

import { useState, useEffect } from "react"
import WaitlistModal from "./WaitlistModal"


const Hero = () => {
  const [email, setEmail] = useState("")
  const [waitlistOpen, setWaitlistOpen] = useState(false);
  const [waitlisted, setWaitlisted] = useState(false);

  const openWaitlistModal = () => setWaitlistOpen(true);
  const closeWaitlistModal = () => setWaitlistOpen(false);

  // Callback for WaitlistModal to notify success
  const handleWaitlisted = () => {
    setWaitlisted(true);
    setWaitlistOpen(false);
  };

  return (
    <>
      <WaitlistModal open={waitlistOpen} onClose={closeWaitlistModal} onWaitlisted={handleWaitlisted} />
      <style>
        {`
          @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700;800&family=Sora:wght@300;400;500;600;700;800&display=swap');
          
          .font-sora {
            font-family: 'Sora', system-ui, sans-serif;
          }
          
          .font-manrope {
            font-family: 'Manrope', system-ui, sans-serif;
          }
          
          .gradient-text {
            background: linear-gradient(135deg, #1e3a8a, #1e40af);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
        `}
      </style>
      <section className="relative min-h-screen overflow-hidden bg-white">
        <div className="relative z-10 mx-auto max-w-7xl px-6 pt-28 pb-14">
          {/* Hero Content and Cards Layout */}
          <div className="max-w-4xl mx-auto">
            {/* Hero Grid */}
            <div className="grid lg:grid-cols-2 gap-12 items-center mb-12">
              {/* Hero Text */}
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="mx-auto max-w-lg"
              >
                <motion.h1
                  className="text-2xl md:text-3xl lg:text-4xl font-bold leading-tight text-slate-800 font-sora mb-6"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2, duration: 0.8 }}
                >
                  We're ending the era of <span className="gradient-text">soulless hiring</span>
                  <br />
                  <span className="gradient-text text-2xl md:text-3xl lg:text-4xl">(with ai)</span>
                </motion.h1>

                <motion.p
                  className="text-lg leading-relaxed text-slate-600 max-w-xl font-manrope mb-6"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.8 }}
                >
                  Connect authentic talent with meaningful opportunities through video resumes that showcase
                  personality, skills, and genuine passion beyond traditional applications.
                </motion.p>

                <motion.div
                  className="flex flex-col sm:flex-row items-start gap-3 max-w-md"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6, duration: 0.8 }}
                >
                  <motion.button
                    className={`flex items-center justify-center gap-2 rounded-xl px-10 py-5 font-semibold shadow-lg transition-all duration-300 whitespace-nowrap font-manrope text-lg w-full ${
                      waitlisted
                        ? "bg-green-500 text-white cursor-not-allowed"
                        : "bg-blue-500 hover:bg-blue-600 text-white shadow-blue-500/25 hover:shadow-xl hover:shadow-blue-500/40"
                    }`}
                    whileHover={waitlisted ? {} : { scale: 1.02, y: -2 }}
                    whileTap={waitlisted ? {} : { scale: 0.98 }}
                    onClick={waitlisted ? undefined : openWaitlistModal}
                    disabled={waitlisted}
                  >
                    {waitlisted ? (
                      <>
                        Waitlisted
                        <CheckCircle className="h-4 w-4" />
                      </>
                    ) : (
                      <>
                        Join Waitlist
                        <ArrowRight className="h-4 w-4" />
                      </>
                    )}
                  </motion.button>
                </motion.div>
              </motion.div>

              {/* Illustration */}
              <motion.div
                className="flex justify-center mx-auto max-w-sm"
                initial={{ opacity: 0, scale: 0.9, y: 30 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
              >
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/30 via-indigo-500/20 to-purple-500/30 rounded-3xl blur-2xl transform translate-x-3 translate-y-3 scale-110"></div>
                  <img
                    src="/hero-visual-vis.png"
                    alt="Modern professional illustration showing video resume concept"
                    className="relative h-auto w-full drop-shadow-2xl rounded-3xl"
                    loading="lazy"
                  />
                </div>
              </motion.div>
            </div>

            {/* Cards Section BELOW hero + illustration */}
            <motion.div
              className="grid lg:grid-cols-2 gap-6 justify-center items-start max-w-6xl mx-auto"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1, duration: 0.8 }}
            >
              <div className="max-w-[600px] w-full mx-auto">
                <JobSeekerCard />
              </div>
              <div className="max-w-[600px] w-full mx-auto">
                <HiringManagerCard />
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </>
  )
}

const JobSeekerCard = () => {
  const [recruiterCount, setRecruiterCount] = useState(150)
  const [currentCompany, setCurrentCompany] = useState(0)

  const companies = [
    "TechCorp",
    "Google",
    "Microsoft",
    "Meta",
    "Apple",
    "Netflix",
    "Spotify",
    "Airbnb",
    "Uber",
    "Stripe",
  ]

  useEffect(() => {
    // Dynamic recruiter count
    const recruiterInterval = setInterval(() => {
      setRecruiterCount((prev) => {
        const change = Math.floor(Math.random() * 10) + 1
        return prev + change
      })
    }, 4000)

    // Company cycling
    const companyInterval = setInterval(() => {
      setCurrentCompany((prev) => (prev + 1) % companies.length)
    }, 3000)

    return () => {
      clearInterval(recruiterInterval)
      clearInterval(companyInterval)
    }
  }, [])

  return (
    <div className="relative">
      {/* Blue gradient shadow */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/25 via-indigo-500/20 to-blue-600/25 rounded-3xl blur-xl transform translate-x-2 translate-y-2"></div>

      <motion.div
        className="relative overflow-hidden rounded-2xl bg-white border-2 border-blue-200/60 p-4 shadow-2xl shadow-blue-500/20"
        style={{ height: "450px" }}
        whileHover={{
          y: -5,
          boxShadow: "0 28px 56px -10px rgba(59, 130, 246, 0.4)",
          scale: 1.02,
        }}
        transition={{ type: "spring", stiffness: 300, damping: 20 }}
      >
        <div className="h-full flex flex-col justify-between">
          {/* Enhanced Header */}
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <div>
                <h3 className="text-lg font-bold text-slate-800 font-sora">For Job Seekers</h3>
                <p className="text-sm text-slate-500 font-manrope">Perfect opportunities await</p>
              </div>
            </div>
          </div>

          {/* Visume Demo Image */}
          <div className="mb-4 flex justify-center">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-indigo-500/20 rounded-xl blur-lg"></div>
              <img
                src="/visumedemo.png"
                alt="Visume demo interface"
                className="relative w-full max-w-[250px] h-auto rounded-xl shadow-lg shadow-blue-500/20 border border-blue-100/50"
                loading="lazy"
              />
            </div>
          </div>

          {/* Dynamic Visume Stats */}
          <div className="space-y-2">
            {/* Recruiter Reach - Dynamic Count */}
            <motion.div
              className="flex items-center gap-3 p-3 rounded-xl border-2 border-emerald-200/60 shadow-md shadow-blue-500/10"
              style={{
                background: "linear-gradient(to right, #ecfdf5, #f0fdf4, #ecfdf5)",
              }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
            >
              <div
                className="w-8 h-8 rounded-xl flex items-center justify-center shadow-md shadow-blue-500/20"
                style={{
                  background: "linear-gradient(135deg, #10b981, #059669)",
                }}
              >
                <Eye className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1">
                <span className="font-bold text-xs font-manrope" style={{ color: "#065f46" }}>
                  Your visume reached{" "}
                  <AnimatePresence mode="wait">
                    <motion.span
                      key={recruiterCount}
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      transition={{ duration: 0.3 }}
                      className="font-bold"
                      style={{ color: "#047857" }}
                    >
                      {recruiterCount}
                    </motion.span>
                  </AnimatePresence>{" "}
                  recruiters
                </span>
              </div>
            </motion.div>

            {/* Company Interest - Dynamic Company */}
            <AnimatePresence mode="wait">
              <motion.div
                key={currentCompany}
                className="flex items-center gap-3 p-3 rounded-xl border-2 border-blue-200/60 shadow-md shadow-blue-500/10"
                style={{
                  background: "linear-gradient(to right, #eff6ff, #dbeafe, #eff6ff)",
                }}
                initial={{ opacity: 0, y: 30, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -20, scale: 0.95 }}
                transition={{
                  duration: 0.5,
                  ease: "easeOut",
                  type: "spring",
                  stiffness: 300,
                  damping: 25,
                }}
              >
                <div
                  className="w-8 h-8 rounded-xl flex items-center justify-center shadow-md shadow-blue-500/20"
                  style={{
                    background: "linear-gradient(135deg, #3b82f6, #1d4ed8)",
                  }}
                >
                  <Building className="h-4 w-4 text-white" />
                </div>
                <div className="flex-1">
                  <span className="font-bold text-xs font-manrope" style={{ color: "#1e3a8a" }}>
                    <span className="font-bold" style={{ color: "#1e40af" }}>
                      {companies[currentCompany]}
                    </span>{" "}
                    is interested in your profile
                  </span>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

const HiringManagerCard = () => {
  const [currentCandidates, setCurrentCandidates] = useState([0, 1, 2])
  const [isSearching, setIsSearching] = useState(false)

  const candidates = [
    {
      name: "Sarah Chen",
      role: "React Developer",
      description: "5+ years experience",
      match: "98%",
      icon: Monitor,
      bgColor: "bg-gradient-to-br from-blue-500 to-blue-600",
    },
    {
      name: "Marcus Johnson",
      role: "Product Designer",
      description: "UI/UX specialist",
      match: "95%",
      icon: Palette,
      bgColor: "bg-gradient-to-br from-purple-500 to-purple-600",
    },
    {
      name: "Elena Rodriguez",
      role: "Full Stack Engineer",
      description: "Node.js expert",
      match: "97%",
      icon: Zap,
      bgColor: "bg-gradient-to-br from-emerald-500 to-emerald-600",
    },
    {
      name: "David Kim",
      role: "UX Researcher",
      description: "Data-driven insights",
      match: "93%",
      icon: Search,
      bgColor: "bg-gradient-to-br from-orange-500 to-orange-600",
    },
    {
      name: "Lisa Wang",
      role: "DevOps Engineer",
      description: "Cloud infrastructure",
      match: "91%",
      icon: Settings,
      bgColor: "bg-gradient-to-br from-red-500 to-red-600",
    },
    {
      name: "Alex Thompson",
      role: "Data Scientist",
      description: "ML specialist",
      match: "89%",
      icon: BarChart3,
      bgColor: "bg-gradient-to-br from-indigo-500 to-indigo-600",
    },
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setIsSearching(true)
      setTimeout(() => {
        setCurrentCandidates((prev) => {
          const nextStart = (prev[0] + 1) % candidates.length
          return [nextStart, (nextStart + 1) % candidates.length, (nextStart + 2) % candidates.length]
        })
        setIsSearching(false)
      }, 1000)
    }, 4500)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="relative">
      {/* Blue gradient shadow */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/25 via-indigo-500/20 to-blue-600/25 rounded-3xl blur-xl transform translate-x-2 translate-y-2"></div>

      <motion.div
        className="relative overflow-hidden rounded-2xl bg-white border-2 border-blue-200/60 p-4 shadow-2xl shadow-blue-500/20"
        style={{ height: "450px" }}
        whileHover={{
          y: -5,
          boxShadow: "0 28px 56px -10px rgba(59, 130, 246, 0.4)",
          scale: 1.02,
        }}
        transition={{ type: "spring", stiffness: 300, damping: 20 }}
      >
        <div className="h-full flex flex-col">
          {/* Enhanced Header */}
          <div className="mb-3">
            <div className="flex items-center gap-2 mb-2">
              <div>
                <h3 className="text-lg font-bold text-slate-800 font-sora">For Hiring Managers</h3>
                <p className="text-sm text-slate-500 font-manrope">AI-powered talent matching</p>
              </div>
            </div>
          </div>

          {/* Enhanced Search Status */}
          <div className="flex items-center gap-2 p-3 rounded-xl bg-gradient-to-r from-indigo-50 via-purple-50 to-indigo-50 border-2 border-indigo-200/60 mb-3 shadow-md shadow-blue-500/10">
            <div className="relative">
              {isSearching ? (
                <Loader2 className="h-4 w-4 text-indigo-600 animate-spin" />
              ) : (
                <div className="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center shadow-sm shadow-blue-500/20">
                  <CheckCircle className="w-2 h-2 text-white" />
                </div>
              )}
            </div>
            <span className="text-slate-700 font-bold font-manrope text-xs">
              {isSearching ? "AI analyzing candidates..." : "54 top candidates analyzed"}
            </span>
          </div>

          {/* Enhanced Candidates List */}
          <div className="space-y-1 flex-1">
            <AnimatePresence mode="sync">
              {currentCandidates.map((candidateIndex, index) => {
                const candidate = candidates[candidateIndex]
                const IconComponent = candidate.icon
                return (
                  <motion.div
                    key={`${candidate.name}-${candidateIndex}-${index}`}
                    className="p-2 rounded-xl bg-gradient-to-r from-white to-indigo-50/50 border-2 border-slate-200/60 shadow-md shadow-blue-500/10 hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300"
                    initial={{ opacity: 0, y: 30, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -20, scale: 0.95 }}
                    transition={{
                      duration: 0.5,
                      delay: index * 0.1,
                      ease: "easeOut",
                      type: "spring",
                      stiffness: 300,
                      damping: 25,
                    }}
                    whileHover={{ scale: 1.02, y: -2 }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        <div
                          className={`w-8 h-8 ${candidate.bgColor} rounded-xl flex items-center justify-center text-white shadow-md shadow-blue-500/20 flex-shrink-0`}
                        >
                          <IconComponent className="h-4 w-4" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <h4 className="text-slate-800 font-bold text-sm font-sora truncate">{candidate.name}</h4>
                          <p className="text-slate-500 font-manrope text-sm truncate">{candidate.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-1 px-2 py-1 rounded-lg border-2 border-emerald-200 bg-gradient-to-r from-emerald-50 to-green-50 shadow-sm shadow-blue-500/10 flex-shrink-0">
                        <span className="font-bold font-sora text-sm text-emerald-700">{candidate.match}</span>
                        <TrendingUp className="h-3 w-3 text-emerald-600" />
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </AnimatePresence>
          </div>

          {/* Enhanced Bottom Notification */}
          <motion.div
            className="flex items-center gap-2 font-medium p-3 rounded-xl bg-gradient-to-r from-indigo-50 via-purple-50 to-indigo-50 border-2 border-indigo-200 font-manrope mt-3 shadow-md shadow-blue-500/10"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            <div className="w-6 h-6 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center shadow-sm shadow-blue-500/20">
              <Sparkles className="h-4 w-4 text-white flex-shrink-0" />
            </div>
            <span className="text-indigo-700 font-bold text-xs">Perfect matches identified! Review now</span>
          </motion.div>
        </div>
      </motion.div>
    </div>
  )
}

export default Hero