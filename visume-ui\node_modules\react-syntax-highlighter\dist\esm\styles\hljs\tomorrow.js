export default {
  "hljs-comment": {
    "color": "#8e908c"
  },
  "hljs-quote": {
    "color": "#8e908c"
  },
  "hljs-variable": {
    "color": "#c82829"
  },
  "hljs-template-variable": {
    "color": "#c82829"
  },
  "hljs-tag": {
    "color": "#c82829"
  },
  "hljs-name": {
    "color": "#c82829"
  },
  "hljs-selector-id": {
    "color": "#c82829"
  },
  "hljs-selector-class": {
    "color": "#c82829"
  },
  "hljs-regexp": {
    "color": "#c82829"
  },
  "hljs-deletion": {
    "color": "#c82829"
  },
  "hljs-number": {
    "color": "#f5871f"
  },
  "hljs-built_in": {
    "color": "#f5871f"
  },
  "hljs-builtin-name": {
    "color": "#f5871f"
  },
  "hljs-literal": {
    "color": "#f5871f"
  },
  "hljs-type": {
    "color": "#f5871f"
  },
  "hljs-params": {
    "color": "#f5871f"
  },
  "hljs-meta": {
    "color": "#f5871f"
  },
  "hljs-link": {
    "color": "#f5871f"
  },
  "hljs-attribute": {
    "color": "#eab700"
  },
  "hljs-string": {
    "color": "#718c00"
  },
  "hljs-symbol": {
    "color": "#718c00"
  },
  "hljs-bullet": {
    "color": "#718c00"
  },
  "hljs-addition": {
    "color": "#718c00"
  },
  "hljs-title": {
    "color": "#4271ae"
  },
  "hljs-section": {
    "color": "#4271ae"
  },
  "hljs-keyword": {
    "color": "#8959a8"
  },
  "hljs-selector-tag": {
    "color": "#8959a8"
  },
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "background": "white",
    "color": "#4d4d4c",
    "padding": "0.5em"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  }
};